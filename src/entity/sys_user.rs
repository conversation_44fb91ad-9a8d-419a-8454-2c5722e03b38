//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use crate::app::enumeration::Gender;
use sea_orm::ActiveValue;
use sea_orm::entity::prelude::*;
use sea_orm::prelude::async_trait::async_trait;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "demo", table_name = "sys_user")]
#[serde(rename_all = "camelCase")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false, column_type = "Text")]
    pub id: String,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub gender: Gender,
    #[sea_orm(column_type = "Text")]
    pub account: String,
    #[sea_orm(column_type = "Text")]
    #[serde(skip_serializing)]
    pub password: String,
    #[sea_orm(column_type = "Text")]
    pub mobile_phone: String,
    pub birthday: Date,
    pub enabled: bool,
    pub created_at: DateTime,
    pub updated_at: DateTime,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

#[async_trait]
impl ActiveModelBehavior for ActiveModel {
    async fn before_save<C>(mut self, _db: &C, insert: bool) -> Result<Self, DbErr>
    where
        C: ConnectionTrait,
    {
        if insert {
            self.id = ActiveValue::set(crate::app::id::next_id());
        }
        Ok(self)
    }
}
